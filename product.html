<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Remove outlines from all elements on product page */
        *:focus,
        *:active,
        *:focus-visible {
            outline: none !important;
        }

        body {
            padding-top: 80px;
        }



        .product-detail {
            padding: 3rem 0;
        }

        .product-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: start;
        }

        .product-gallery {
            position: sticky;
            top: 100px;
        }

        .main-image {
            width: 100%;
            height: 600px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 1rem;
            position: relative;
        }

        .main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: zoom-in;
        }

        .image-thumbnails {
            display: flex;
            gap: 0.5rem;
            overflow-x: auto;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.3s ease;
        }

        .thumbnail.active {
            border-color: #4B0082;
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-info {
            padding-left: 2rem;
        }

        .product-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #333;
        }

        /* Dark mode styles for product title */
        [data-theme="dark"] .product-title,
        body.dark-theme .product-title {
            color: var(--text-color);
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .rating-stars {
            color: #ffc107;
        }

        .rating-text {
            color: #666;
        }

        /* Dark mode styles for rating text */
        [data-theme="dark"] .rating-text,
        body.dark-theme .rating-text {
            color: var(--text-light);
        }

        .product-price {
            margin-bottom: 2rem;
        }

        .current-price {
            font-size: 2rem;
            font-weight: 700;
            color: #4B0082;
            margin-right: 1rem;
        }

        .original-price {
            font-size: 1.5rem;
            color: #999;
            text-decoration: line-through;
            margin-right: 1rem;
        }

        /* Dark mode styles for pricing */
        [data-theme="dark"] .current-price,
        body.dark-theme .current-price {
            color: #8b5cf6;
        }

        [data-theme="dark"] .original-price,
        body.dark-theme .original-price {
            color: var(--text-light);
        }

        .discount-badge {
            background: #e74c3c;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .product-options {
            margin-bottom: 2rem;
        }

        .option-group {
            margin-bottom: 1.5rem;
        }

        .option-label {
            display: block;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #333;
        }

        /* Dark mode styles for option labels */
        [data-theme="dark"] .option-label,
        body.dark-theme .option-label {
            color: var(--text-color);
        }

        .size-options, .color-options {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .size-option {
            padding: 8px 16px;
            border: 2px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            color: #333;
        }

        .size-option:hover {
            border-color: #4B0082;
        }

        .size-option.selected {
            border-color: #4B0082;
            background: #4B0082;
            color: white;
        }

        /* Dark mode styles for size options */
        [data-theme="dark"] .size-option,
        body.dark-theme .size-option {
            background: var(--card-bg);
            border-color: var(--border-color);
            color: var(--text-color);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        [data-theme="dark"] .size-option:hover,
        body.dark-theme .size-option:hover {
            border-color: #8b5cf6;
            background: rgba(139, 92, 246, 0.15);
            color: #a78bfa;
            box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
        }

        [data-theme="dark"] .size-option.selected,
        body.dark-theme .size-option.selected {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #8b5cf6, #a78bfa);
            color: white;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        [data-theme="dark"] .size-option.selected:hover,
        body.dark-theme .size-option.selected:hover {
            background: linear-gradient(135deg, #7c3aed, #8b5cf6);
            box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            position: relative;
        }

        .color-option.selected::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
        }

        .quantity-selector {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .quantity-label {
            font-weight: 600;
            color: #333;
        }

        .quantity-controls {
            display: flex;
            align-items: center;
            border: 2px solid #ddd;
            border-radius: 6px;
            overflow: hidden;
        }

        .quantity-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
            color: #333;
        }

        .quantity-btn:hover {
            background: #e9ecef;
        }

        .quantity-input {
            width: 60px;
            height: 40px;
            border: none;
            text-align: center;
            font-weight: 600;
            background: white;
            color: #333;
        }

        /* Dark mode styles for quantity controls */
        [data-theme="dark"] .quantity-label,
        body.dark-theme .quantity-label {
            color: var(--text-color);
        }

        [data-theme="dark"] .quantity-controls,
        body.dark-theme .quantity-controls {
            border-color: var(--border-color);
        }

        [data-theme="dark"] .quantity-btn,
        body.dark-theme .quantity-btn {
            background: var(--card-bg);
            color: var(--text-color);
            border-right: 1px solid var(--border-color);
        }

        [data-theme="dark"] .quantity-btn:hover,
        body.dark-theme .quantity-btn:hover {
            background: rgba(139, 92, 246, 0.1);
        }

        [data-theme="dark"] .quantity-input,
        body.dark-theme .quantity-input {
            background: var(--input-bg);
            color: var(--text-color);
        }

        .product-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .add-to-cart {
            flex: 2;
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            border: none;
            padding: 18px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(75, 0, 130, 0.2);
        }

        .add-to-cart:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(75, 0, 130, 0.4);
            background: linear-gradient(135deg, #5a0099, #7b1fa2);
        }

        .add-to-cart:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(75, 0, 130, 0.3);
        }

        .favorite-btn {
            flex: 1;
            min-width: 140px;
            height: 60px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .favorite-btn:hover {
            border-color: #4B0082;
            color: #4B0082;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(75, 0, 130, 0.2);
        }

        .favorite-btn.active {
            background: #4B0082;
            border-color: #4B0082;
            color: white;
        }

        .favorite-btn.active:hover {
            background: #5a0099;
            border-color: #5a0099;
        }

        .favorite-text {
            font-size: 0.9rem;
        }

        .secondary-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .btn-secondary {
            flex: 1;
            padding: 18px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            color: white;
        }

        .btn-secondary:first-child {
            background: linear-gradient(135deg, #4B0082, #5a0099);
            box-shadow: 0 4px 15px rgba(75, 0, 130, 0.2);
        }

        .btn-secondary:first-child:hover {
            background: linear-gradient(135deg, #5a0099, #6b00b3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(75, 0, 130, 0.4);
        }

        .btn-secondary:last-child {
            background: linear-gradient(135deg, #17a2b8, #138496);
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.2);
        }

        .btn-secondary:last-child:hover {
            background: linear-gradient(135deg, #138496, #0f6674);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
        }

        .btn-secondary:active {
            transform: translateY(0);
        }

        .btn-secondary i {
            font-size: 1.2rem;
        }

        /* Favorites Section Styles */
        .favorites-section {
            margin-bottom: 2rem;
            text-align: center;
        }

        .btn-favorites-large {
            width: 100%;
            max-width: 400px;
            padding: 18px 30px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
        }

        .btn-favorites-large:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
        }

        .btn-favorites-large:active {
            transform: translateY(0);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-favorites-large.active {
            background: linear-gradient(135deg, #27ae60, #229954);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.2);
        }

        .btn-favorites-large.active:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        }

        .btn-favorites-large i {
            font-size: 1.2rem;
        }

        .favorites-text {
            font-size: 1.1rem;
        }

        /* User Rating Section Styles */
        .user-rating-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 1.5rem;
            text-align: center;
        }

        .user-rating-section h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .rating-description {
            margin: 0 0 1rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .user-rating-container {
            margin-bottom: 1rem;
        }

        .user-star-rating {
            display: inline-flex;
            gap: 4px;
            margin-bottom: 0.5rem;
        }

        .user-star {
            font-size: 2rem;
            color: #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .user-star:hover,
        .user-star.active {
            color: #ffc107;
            transform: scale(1.1);
        }

        .user-star.hover-effect {
            color: #ffc107;
        }

        .user-rating-text {
            display: block;
            color: #666;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        .rating-actions {
            margin-top: 1rem;
        }

        .rating-actions textarea {
            width: 100%;
            max-width: 400px;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: inherit;
            font-size: 0.9rem;
            resize: vertical;
            margin-bottom: 1rem;
        }

        .rating-actions textarea:focus {
            outline: none;
            border-color: #4B0082;
            box-shadow: 0 0 0 2px rgba(75, 0, 130, 0.1);
        }

        .rating-buttons {
            display: flex;
            gap: 0.75rem;
            justify-content: center;
        }

        .btn-rating-submit,
        .btn-rating-cancel {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-rating-submit {
            background: #4B0082;
            color: white;
        }

        .btn-rating-submit:hover {
            background: #5a0099;
            transform: translateY(-1px);
        }

        .btn-rating-cancel {
            background: #6c757d;
            color: white;
        }

        .btn-rating-cancel:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .user-rating-status {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .user-rating-status i {
            color: #28a745;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .product-actions {
                flex-direction: column;
                gap: 0.8rem;
            }

            .add-to-cart {
                flex: none;
                width: 100%;
            }

            .favorite-btn {
                flex: none;
                width: 100%;
                min-width: auto;
            }

            .secondary-actions {
                flex-direction: column;
                gap: 0.8rem;
            }

            .btn-secondary {
                flex: none;
                width: 100%;
                padding: 15px 20px;
                font-size: 1rem;
            }

            .favorite-text {
                display: inline;
            }
        }

        @media (max-width: 480px) {
            .product-actions {
                gap: 0.6rem;
            }

            .add-to-cart {
                padding: 15px 20px;
                font-size: 1rem;
            }

            .favorite-btn {
                height: 50px;
                font-size: 0.9rem;
            }



            .secondary-actions {
                gap: 0.6rem;
            }

            .favorites-section {
                margin-bottom: 1.5rem;
            }

            .btn-favorites-large {
                width: 100%;
                padding: 15px 20px;
                font-size: 1rem;
            }

            .favorites-text {
                font-size: 1rem;
            }

            .user-rating-section {
                padding: 1rem;
                margin-top: 1rem;
            }

            .user-star {
                font-size: 1.8rem;
            }

            .rating-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }

            .btn-rating-submit,
            .btn-rating-cancel {
                width: 100%;
                justify-content: center;
            }
        }



        .product-tabs {
            margin-top: 3rem;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #eee;
            margin-bottom: 2rem;
        }

        .tab-btn {
            padding: 1rem 2rem;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 500;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            color: #4B0082;
            border-bottom-color: #4B0082;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .reviews-summary {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .rating-overview {
            text-align: center;
        }

        .rating-number {
            font-size: 3rem;
            font-weight: 700;
            color: #4B0082;
        }

        .rating-bars {
            flex: 1;
        }

        .rating-bar {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .bar-fill {
            flex: 1;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
        }

        .bar-progress {
            height: 100%;
            background: #ffc107;
            transition: width 0.3s ease;
        }

        .review-item {
            border-bottom: 1px solid #eee;
            padding: 1.5rem 0;
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .reviewer-name {
            font-weight: 600;
        }

        .review-date {
            color: #666;
            font-size: 0.9rem;
        }

        .review-rating {
            color: #ffc107;
            margin-bottom: 0.5rem;
        }

        .review-text {
            color: #666;
            line-height: 1.6;
        }

        /* Related Products Section Styling */
        .related-products {
            padding: 4rem 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }

        .related-products::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(75, 0, 130, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(216, 191, 216, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .related-products-header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
            z-index: 1;
        }

        .related-products-header .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #4B0082;
            margin-bottom: 0.5rem;
            position: relative;
        }

        .related-products-header .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: linear-gradient(135deg, #4B0082, #D8BFD8);
            border-radius: 2px;
        }

        .section-subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-top: 1rem;
        }

        .related-products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            position: relative;
            z-index: 1;
        }

        /* Related Product Card Styling */
        .related-product-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .related-product-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .related-product-image {
            position: relative;
            height: 250px;
            overflow: hidden;
        }

        .related-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .related-product-card:hover .related-product-image img {
            transform: scale(1.05);
        }

        .related-product-overlay {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .related-product-card:hover .related-product-overlay {
            opacity: 1;
        }

        .related-action-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .related-action-btn:hover {
            background: #4B0082;
            color: white;
            transform: scale(1.1);
        }

        .related-discount-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .related-product-info {
            padding: 1.5rem;
        }

        .related-product-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .related-product-price {
            margin-bottom: 0.8rem;
        }

        .related-current-price {
            font-size: 1.3rem;
            font-weight: 700;
            color: #4B0082;
        }

        .related-original-price {
            font-size: 1rem;
            color: #999;
            text-decoration: line-through;
            margin-left: 0.5rem;
        }

        .related-product-rating {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 1rem;
        }

        .related-add-to-cart {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .related-add-to-cart:hover {
            background: linear-gradient(135deg, #3a0066, #5a1a7a);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(75, 0, 130, 0.3);
        }

        /* Responsive styling for related products */
        @media (max-width: 768px) {
            .related-products-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
            }

            .related-products-header .section-title {
                font-size: 2rem;
            }

            .related-product-overlay {
                opacity: 1; /* Always show on mobile */
            }
        }

        @media (max-width: 480px) {
            .related-products {
                padding: 2rem 0;
            }

            .related-products-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .related-products-header .section-title {
                font-size: 1.8rem;
            }

            .related-product-image {
                height: 200px;
            }
        }

        /* Enhanced Reviews/Comments Section Styling */
        .write-review-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            border: 1px solid #e9ecef;
        }

        .write-review-section h4 {
            color: #4B0082;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
            font-weight: 600;
        }

        .review-form {
            max-width: 600px;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4B0082;
            box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.1);
        }

        .star-rating {
            display: flex;
            gap: 5px;
            margin-bottom: 0.5rem;
        }

        .star-rating .star {
            font-size: 2rem;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .star-rating .star:hover,
        .star-rating .star.active {
            color: #ffc107;
        }

        .rating-text {
            color: #666;
            font-size: 0.9rem;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin-right: 0.5rem;
        }

        .submit-review-btn {
            background: linear-gradient(135deg, #4B0082, #6a1b9a);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .submit-review-btn:hover {
            background: linear-gradient(135deg, #3a0066, #5a1a7a);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(75, 0, 130, 0.3);
        }

        .reviews-controls {
            display: flex;
            gap: 2rem;
            margin: 2rem 0;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .reviews-filter,
        .reviews-sort {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .reviews-filter label,
        .reviews-sort label {
            font-weight: 500;
            color: #333;
            white-space: nowrap;
        }

        .reviews-filter select,
        .reviews-sort select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
        }

        /* Enhanced Review Item Styling */
        .review-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: box-shadow 0.3s ease;
        }

        .review-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .reviewer-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .reviewer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #4B0082, #D8BFD8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .reviewer-details {
            display: flex;
            flex-direction: column;
        }

        .reviewer-name {
            font-weight: 600;
            color: #333;
        }

        .review-date {
            font-size: 0.85rem;
            color: #666;
        }

        .verified-badge {
            background: #27ae60;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .review-rating {
            margin: 0.5rem 0;
        }

        .review-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .review-text {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .review-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        .review-action-btn {
            background: none;
            border: 1px solid #ddd;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .review-action-btn:hover {
            border-color: #4B0082;
            color: #4B0082;
        }

        .review-action-btn.active {
            background: #4B0082;
            color: white;
            border-color: #4B0082;
        }

        .reply-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        .reply-form {
            display: none;
            margin-top: 1rem;
        }

        .reply-form.active {
            display: block;
        }

        .reply-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            min-height: 80px;
        }

        .reply-buttons {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .reply-submit,
        .reply-cancel {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .reply-submit {
            background: #4B0082;
            color: white;
        }

        .reply-cancel {
            background: #6c757d;
            color: white;
        }

        .replies-list {
            margin-top: 1rem;
            padding-left: 2rem;
            border-left: 3px solid #f0f0f0;
        }

        .reply-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        /* Responsive styling for reviews */
        @media (max-width: 768px) {
            .reviews-controls {
                flex-direction: column;
                gap: 1rem;
            }

            .review-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .review-actions {
                flex-wrap: wrap;
            }

            .replies-list {
                padding-left: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="logo">
                    <a href="index.html">
                        <svg width="150" height="40" viewBox="0 0 150 40" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#4B0082;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#D8BFD8;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <text x="5" y="28" font-family="Inter, sans-serif" font-size="16" font-weight="700" fill="url(#logoGradient)">The Project Faith</text>
                        </svg>
                    </a>
                </div>
            </div>

            <div class="nav-center">
                <ul class="nav-links" id="navLinks">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html">Products</a></li>
                    <li><a href="sale.html">Sale</a></li>
                </ul>
                <div class="search-box">
                    <input type="text" placeholder="Search for products..." id="searchInput">
                    <button class="search-btn"><i class="fas fa-search"></i></button>
                </div>
            </div>

            <div class="nav-right">
                <div class="nav-icons">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <a href="login.html" class="nav-icon">
                        <i class="fas fa-user"></i>
                    </a>
                    <button class="nav-icon" id="favoritesBtn">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="favoritesCount">0</span>
                    </button>
                    <button class="nav-icon" id="cartBtn">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>



    <!-- Hero Section -->
    <section class="page-hero">
        <div class="page-hero-content">
            <h1>Product Details</h1>
            <p>Discover the perfect style for you</p>
        </div>
    </section>

    <!-- Product Detail -->
    <section class="product-detail">
        <div class="container">
            <div class="product-container">
                <!-- Product Gallery -->
                <div class="product-gallery">
                    <div class="main-image">
                        <img id="mainImage" src="" alt="Product Image">
                    </div>
                    <div class="image-thumbnails" id="thumbnails">
                        <!-- Thumbnails will be loaded dynamically -->
                    </div>
                </div>

                <!-- Product Info -->
                <div class="product-info">
                    <h1 class="product-title" id="productTitle">Loading...</h1>

                    <div class="product-rating">
                        <div class="rating-stars" id="productRating">
                            <!-- Stars will be loaded dynamically -->
                        </div>
                        <span class="rating-text" id="ratingText">(0 reviews)</span>
                    </div>

                    <div class="product-price">
                        <span class="current-price" id="currentPrice">$0.00</span>
                        <span class="original-price" id="originalPrice" style="display: none;">$0.00</span>
                        <span class="discount-badge" id="discountBadge" style="display: none;">0% OFF</span>
                    </div>

                    <div class="product-options">
                        <div class="option-group" id="sizeGroup" style="display: none;">
                            <label class="option-label">Size:</label>
                            <div class="size-options" id="sizeOptions">
                                <!-- Size options will be loaded dynamically -->
                            </div>
                        </div>
                    </div>

                    <div class="quantity-selector">
                        <span class="quantity-label">Quantity:</span>
                        <div class="quantity-controls">
                            <button class="quantity-btn" id="decreaseQty">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" class="quantity-input" id="quantity" value="1" min="1" max="10">
                            <button class="quantity-btn" id="increaseQty">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <div class="product-actions">
                        <button class="add-to-cart" id="addToCartBtn">
                            <i class="fas fa-shopping-cart"></i>
                            Add to Cart
                        </button>
                        <button class="favorite-btn" id="favoriteBtn" title="Add to Favorites">
                            <i class="far fa-heart"></i>
                            <span class="favorite-text">Add to Favorites</span>
                        </button>
                    </div>

                    <!-- Additional Action Buttons -->
                    <div class="secondary-actions">
                        <button class="btn-secondary" id="addToCartSecondaryBtn">
                            <i class="fas fa-shopping-cart"></i>
                            Add to Cart
                        </button>
                        <button class="btn-secondary" id="shareBtn">
                            <i class="fas fa-share-alt"></i>
                            Share
                        </button>
                    </div>

                    <!-- Add to Favorites Section -->
                    <div class="favorites-section">
                        <button class="btn-favorites-large" id="favoriteBtnLarge" title="Add to Favorites">
                            <i class="far fa-heart"></i>
                            <span class="favorites-text">Add to Favorites</span>
                        </button>

                        <!-- User Rating Section -->
                        <div class="user-rating-section">
                            <h4>Rate this Product</h4>
                            <p class="rating-description">Share your experience with other customers</p>
                            <div class="user-rating-container">
                                <div class="user-star-rating" id="userStarRating">
                                    <span class="user-star" data-rating="1">★</span>
                                    <span class="user-star" data-rating="2">★</span>
                                    <span class="user-star" data-rating="3">★</span>
                                    <span class="user-star" data-rating="4">★</span>
                                    <span class="user-star" data-rating="5">★</span>
                                </div>
                                <span class="user-rating-text" id="userRatingText">Click to rate</span>
                            </div>
                            <div class="rating-actions" id="ratingActions" style="display: none;">
                                <textarea id="quickReviewText" placeholder="Share a quick review (optional)" rows="3"></textarea>
                                <div class="rating-buttons">
                                    <button class="btn-rating-submit" id="submitRatingBtn">
                                        <i class="fas fa-check"></i>
                                        Submit Rating
                                    </button>
                                    <button class="btn-rating-cancel" id="cancelRatingBtn">
                                        <i class="fas fa-times"></i>
                                        Cancel
                                    </button>
                                </div>
                            </div>
                            <div class="user-rating-status" id="userRatingStatus" style="display: none;">
                                <i class="fas fa-check-circle"></i>
                                <span>Thank you for rating this product!</span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Product Tabs -->
            <div class="product-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" data-tab="description">Description</button>
                    <button class="tab-btn" data-tab="specifications">Specifications</button>
                    <button class="tab-btn" data-tab="reviews">Reviews</button>
                    <button class="tab-btn" data-tab="shipping">Shipping</button>
                </div>

                <div class="tab-content active" id="description">
                    <h3>Product Description</h3>
                    <p id="productDescription">Loading product description...</p>
                </div>

                <div class="tab-content" id="specifications">
                    <h3>Specifications</h3>
                    <div id="productSpecs">
                        <!-- Specifications will be loaded dynamically -->
                    </div>
                </div>

                <div class="tab-content" id="reviews">
                    <h3>Customer Reviews & Comments</h3>

                    <!-- Reviews Summary -->
                    <div class="reviews-summary">
                        <div class="rating-overview">
                            <div class="rating-number" id="avgRating">0.0</div>
                            <div class="rating-stars" id="avgRatingStars"></div>
                            <div>Based on <span id="totalReviews">0</span> reviews</div>
                        </div>
                        <div class="rating-bars">
                            <div class="rating-bar">
                                <span>5★</span>
                                <div class="bar-fill">
                                    <div class="bar-progress" style="width: 70%"></div>
                                </div>
                                <span>70%</span>
                            </div>
                            <div class="rating-bar">
                                <span>4★</span>
                                <div class="bar-fill">
                                    <div class="bar-progress" style="width: 20%"></div>
                                </div>
                                <span>20%</span>
                            </div>
                            <div class="rating-bar">
                                <span>3★</span>
                                <div class="bar-fill">
                                    <div class="bar-progress" style="width: 7%"></div>
                                </div>
                                <span>7%</span>
                            </div>
                            <div class="rating-bar">
                                <span>2★</span>
                                <div class="bar-fill">
                                    <div class="bar-progress" style="width: 2%"></div>
                                </div>
                                <span>2%</span>
                            </div>
                            <div class="rating-bar">
                                <span>1★</span>
                                <div class="bar-fill">
                                    <div class="bar-progress" style="width: 1%"></div>
                                </div>
                                <span>1%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Write Review Section -->
                    <div class="write-review-section">
                        <h4>Write a Review</h4>
                        <div class="review-form" id="reviewForm">
                            <div class="form-group">
                                <label for="reviewerName">Your Name:</label>
                                <input type="text" id="reviewerName" placeholder="Enter your name" required>
                            </div>

                            <div class="form-group">
                                <label for="reviewRating">Your Rating:</label>
                                <div class="star-rating" id="starRating">
                                    <span class="star" data-rating="1">★</span>
                                    <span class="star" data-rating="2">★</span>
                                    <span class="star" data-rating="3">★</span>
                                    <span class="star" data-rating="4">★</span>
                                    <span class="star" data-rating="5">★</span>
                                </div>
                                <span class="rating-text" id="ratingText">Click to rate</span>
                            </div>

                            <div class="form-group">
                                <label for="reviewTitle">Review Title:</label>
                                <input type="text" id="reviewTitle" placeholder="Summarize your experience" required>
                            </div>

                            <div class="form-group">
                                <label for="reviewText">Your Review:</label>
                                <textarea id="reviewText" rows="4" placeholder="Share your thoughts about this product..." required></textarea>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="verifiedPurchase">
                                    <span class="checkmark"></span>
                                    I purchased this product
                                </label>
                            </div>

                            <button type="submit" class="submit-review-btn" id="submitReview">
                                <i class="fas fa-paper-plane"></i>
                                Submit Review
                            </button>
                        </div>
                    </div>

                    <!-- Reviews Filter and Sort -->
                    <div class="reviews-controls">
                        <div class="reviews-filter">
                            <label for="filterRating">Filter by rating:</label>
                            <select id="filterRating">
                                <option value="all">All ratings</option>
                                <option value="5">5 stars</option>
                                <option value="4">4 stars</option>
                                <option value="3">3 stars</option>
                                <option value="2">2 stars</option>
                                <option value="1">1 star</option>
                            </select>
                        </div>

                        <div class="reviews-sort">
                            <label for="sortReviews">Sort by:</label>
                            <select id="sortReviews">
                                <option value="newest">Newest first</option>
                                <option value="oldest">Oldest first</option>
                                <option value="highest">Highest rated</option>
                                <option value="lowest">Lowest rated</option>
                                <option value="helpful">Most helpful</option>
                            </select>
                        </div>
                    </div>

                    <!-- Reviews List -->
                    <div id="reviewsList">
                        <!-- Reviews will be loaded dynamically -->
                    </div>
                </div>

                <div class="tab-content" id="shipping">
                    <h3>Shipping Information</h3>
                    <div class="shipping-info">
                        <h4>Delivery Options</h4>
                        <ul>
                            <li><strong>Standard Shipping (5-7 business days):</strong> Free on orders over $50, otherwise $5.99</li>
                            <li><strong>Express Shipping (2-3 business days):</strong> $12.99</li>
                            <li><strong>Next Day Delivery:</strong> $24.99 (order by 2 PM)</li>
                        </ul>

                        <h4>International Shipping</h4>
                        <p>We ship worldwide! International shipping rates vary by destination and are calculated at checkout.</p>

                        <h4>Return Policy</h4>
                        <p>Not satisfied? Return your item within 30 days for a full refund. Items must be in original condition with tags attached.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    <section class="related-products">
        <div class="container">
            <div class="related-products-header">
                <h2 class="section-title">You Might Also Like</h2>
                <p class="section-subtitle">Discover more products that complement your style</p>
            </div>
            <div class="related-products-grid" id="relatedProducts">
                <!-- Related products will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>VAITH</h3>
                    <p>Your destination for trendy and affordable fashion.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">Shipping Info</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <ul>
                        <li><a href="#">About Us</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 VAITH. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Cart Modal -->
    <div class="modal" id="cartModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Shopping Cart</h3>
                <button class="close-modal" id="closeCart">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="cartItems">
                <!-- Cart items will be loaded dynamically -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <strong>Total: $<span id="cartTotal">0.00</span></strong>
                </div>
                <button class="checkout-btn">Checkout</button>
            </div>
        </div>
    </div>

    <!-- Favorites Modal -->
    <div class="modal" id="favoritesModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Your Favorites</h3>
                <button class="close-modal" id="closeFavorites">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="favoritesItems">
                <!-- Favorites will be loaded dynamically -->
            </div>
        </div>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>

    <script>
        // Product page specific functionality
        let currentProduct = null;
        let selectedSize = null;

        // Sample product data (in a real app, this would come from an API)
        const productData = {
            1: {
                id: 1,
                title: "Summer Floral Dress",
                price: 29.99,
                originalPrice: 49.99,
                images: [
                    "https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=600&h=800&fit=crop&auto=format&q=80",
                    "https://images.unsplash.com/photo-1509631179647-0177331693ae?w=600&h=800&fit=crop&auto=format&q=80",
                    "https://images.unsplash.com/photo-1469334031218-e382a71b716b?w=600&h=800&fit=crop&auto=format&q=80"
                ],
                rating: 4.5,
                reviews: 128,
                category: "women",
                description: "This beautiful summer floral dress is perfect for warm weather occasions. Made from lightweight, breathable fabric with a flattering fit that's comfortable all day long. Features a vibrant floral print that adds a touch of elegance to your summer wardrobe.",
                sizes: ["XS", "S", "M", "L", "XL"],
                specifications: {
                    "Material": "100% Cotton",
                    "Care Instructions": "Machine wash cold, tumble dry low",
                    "Origin": "Made in USA",
                    "Fit": "Regular fit",
                    "Length": "Midi length"
                }
            },
            2: {
                id: 2,
                title: "Classic White Shirt",
                price: 24.99,
                originalPrice: 34.99,
                images: [
                    "https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=600&h=800&fit=crop&auto=format&q=80",
                    "https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=600&h=800&fit=crop&auto=format&q=80"
                ],
                rating: 4.2,
                reviews: 89,
                category: "men",
                description: "A timeless classic white shirt that's perfect for both casual and formal occasions. Crafted from premium cotton with a comfortable fit and clean lines.",
                sizes: ["S", "M", "L", "XL", "XXL"],
                specifications: {
                    "Material": "100% Premium Cotton",
                    "Care Instructions": "Machine wash warm, iron if needed",
                    "Origin": "Made in Portugal",
                    "Fit": "Slim fit",
                    "Collar": "Spread collar"
                }
            },
            3: {
                id: 3,
                title: "Denim Jacket",
                price: 59.99,
                originalPrice: null,
                images: [
                    "https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=600&h=800&fit=crop&auto=format&q=80",
                    "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=800&fit=crop&auto=format&q=80",
                    "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=800&fit=crop&auto=format&q=80"
                ],
                rating: 4.7,
                reviews: 203,
                category: "women",
                description: "A versatile denim jacket that's perfect for layering. Made from high-quality denim with a classic fit that never goes out of style. Perfect for casual outings and adding a touch of edge to any outfit.",
                sizes: ["XS", "S", "M", "L", "XL"],
                specifications: {
                    "Material": "100% Cotton Denim",
                    "Care Instructions": "Machine wash cold, hang dry",
                    "Origin": "Made in Turkey",
                    "Fit": "Regular fit",
                    "Style": "Classic denim jacket"
                }
            }
        };

        document.addEventListener('DOMContentLoaded', function() {
            initializeProductPage();
        });

        function initializeProductPage() {
            // Get product ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id');

            // Load all available products (hardcoded + localStorage)
            const allProducts = getAllProducts();

            if (productId && allProducts[productId]) {
                currentProduct = allProducts[productId];
                loadProduct(currentProduct);
            } else if (productId) {
                // Product ID specified but not found
                showNotification(`Product not found. Showing default product.`, 'warning');
                // Try to load first available product
                const firstProductId = Object.keys(allProducts)[0];
                if (firstProductId) {
                    currentProduct = allProducts[firstProductId];
                    loadProduct(currentProduct);
                    // Update URL to reflect the actual product being shown
                    window.history.replaceState({}, '', `product.html?id=${firstProductId}`);
                } else {
                    // No products available at all
                    showNotification('No products available.', 'error');
                    return;
                }
            } else {
                // No product ID specified, default to first product
                const firstProductId = Object.keys(allProducts)[0];
                if (firstProductId) {
                    currentProduct = allProducts[firstProductId];
                    loadProduct(currentProduct);
                    // Update URL to include the product ID
                    window.history.replaceState({}, '', `product.html?id=${firstProductId}`);
                } else {
                    // No products available at all
                    showNotification('No products available.', 'error');
                    return;
                }
            }

            // Initialize event listeners
            initializeProductEvents();
            initializeTabs();
        }

        // Function to get all products from both hardcoded data and localStorage
        function getAllProducts() {
            const allProducts = { ...productData }; // Start with hardcoded products

            // Load products from localStorage (admin-added products)
            try {
                const storedProducts = localStorage.getItem('vaith_products');
                if (storedProducts) {
                    const adminProducts = JSON.parse(storedProducts);
                    adminProducts.forEach(product => {
                        // Convert admin product format to product page format
                        // Use string key to match URL parameter format
                        allProducts[String(product.id)] = {
                            id: product.id,
                            title: product.name,
                            price: product.price,
                            originalPrice: product.originalPrice,
                            images: product.images || ['https://via.placeholder.com/400x500?text=No+Image'],
                            rating: product.rating || 0,
                            reviews: product.reviews || 0,
                            category: product.category,
                            description: product.description || 'No description available',
                            sizes: product.sizes || ['One Size'],
                            colors: product.colors || ['Default'],
                            specifications: {
                                "Brand": product.brand || 'VAITH',
                                "SKU": product.sku || `SKU-${product.id}`,
                                "Category": product.category,
                                "Stock": product.stock || 0,
                                "Status": product.status || 'active'
                            }
                        };
                    });
                }
            } catch (error) {
                // Error loading products from localStorage - continue with hardcoded products only
            }

            return allProducts;
        }

        function loadProduct(product) {
            // Update page title
            document.title = `${product.title} - The Project Faith`;

            // Load product images
            loadProductImages(product.images);

            // Load product info
            document.getElementById('productTitle').textContent = product.title;
            document.getElementById('currentPrice').textContent = `$${product.price}`;

            if (product.originalPrice) {
                document.getElementById('originalPrice').textContent = `$${product.originalPrice}`;
                document.getElementById('originalPrice').style.display = 'inline';

                const discount = Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
                document.getElementById('discountBadge').textContent = `${discount}% OFF`;
                document.getElementById('discountBadge').style.display = 'inline';
            }

            // Load rating
            document.getElementById('productRating').innerHTML = generateStars(product.rating);
            document.getElementById('ratingText').textContent = `(${product.reviews} reviews)`;

            // Load description
            document.getElementById('productDescription').textContent = product.description;

            // Load sizes
            if (product.sizes && product.sizes.length > 0) {
                loadSizes(product.sizes);
                document.getElementById('sizeGroup').style.display = 'block';
            }

            // Load specifications
            loadSpecifications(product.specifications);

            // Load reviews
            loadReviews(product);

            // Load related products
            loadRelatedProducts();

            // Update favorite button state
            updateFavoriteButton(product.id);
            updateLargeFavoriteButton(product.id);
        }

        function loadProductImages(images) {
            const mainImage = document.getElementById('mainImage');
            const thumbnailsContainer = document.getElementById('thumbnails');

            // Set main image
            mainImage.src = images[0];
            mainImage.alt = currentProduct.title;

            // Create thumbnails
            thumbnailsContainer.innerHTML = '';
            images.forEach((image, index) => {
                const thumbnail = document.createElement('div');
                thumbnail.className = `thumbnail ${index === 0 ? 'active' : ''}`;
                thumbnail.innerHTML = `<img src="${image}" alt="Product image ${index + 1}">`;

                thumbnail.addEventListener('click', () => {
                    mainImage.src = image;
                    document.querySelectorAll('.thumbnail').forEach(t => t.classList.remove('active'));
                    thumbnail.classList.add('active');
                });

                thumbnailsContainer.appendChild(thumbnail);
            });
        }

        function loadSizes(sizes) {
            const container = document.getElementById('sizeOptions');
            container.innerHTML = '';

            sizes.forEach(size => {
                const sizeOption = document.createElement('div');
                sizeOption.className = 'size-option';
                sizeOption.textContent = size;
                sizeOption.addEventListener('click', () => selectSize(size, sizeOption));
                container.appendChild(sizeOption);
            });
        }

        function loadColors(colors) {
            const container = document.getElementById('colorOptions');
            container.innerHTML = '';

            colors.forEach(color => {
                const colorOption = document.createElement('div');
                colorOption.className = 'color-option';
                colorOption.style.backgroundColor = color.value;
                colorOption.title = color.name;
                colorOption.addEventListener('click', () => selectColor(color, colorOption));
                container.appendChild(colorOption);
            });
        }

        function selectSize(size, element) {
            selectedSize = size;
            document.querySelectorAll('.size-option').forEach(el => el.classList.remove('selected'));
            element.classList.add('selected');
        }

        function selectColor(color, element) {
            selectedColor = color;
            document.querySelectorAll('.color-option').forEach(el => el.classList.remove('selected'));
            element.classList.add('selected');
        }

        function loadSpecifications(specs) {
            const container = document.getElementById('productSpecs');
            container.innerHTML = '';

            const table = document.createElement('table');
            table.style.width = '100%';
            table.style.borderCollapse = 'collapse';

            Object.entries(specs).forEach(([key, value]) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td style="padding: 0.5rem; border-bottom: 1px solid #eee; font-weight: 600; width: 30%;">${key}</td>
                    <td style="padding: 0.5rem; border-bottom: 1px solid #eee;">${value}</td>
                `;
                table.appendChild(row);
            });

            container.appendChild(table);
        }

        // Global reviews data
        let allReviews = [];
        let currentUserRating = 0;

        function loadReviews(product) {
            // Update review summary
            document.getElementById('avgRating').textContent = product.rating.toFixed(1);
            document.getElementById('avgRatingStars').innerHTML = generateStars(product.rating);
            document.getElementById('totalReviews').textContent = product.reviews;

            // Generate comprehensive sample reviews
            allReviews = generateSampleReviews(product);

            // Display reviews
            displayReviews(allReviews);

            // Initialize review form functionality
            initializeReviewForm();

            // Initialize review controls
            initializeReviewControls();
        }

        function generateSampleReviews(product) {
            const reviewTemplates = [
                {
                    name: "Sarah M.",
                    rating: 5,
                    date: "2024-01-15",
                    title: "Absolutely Perfect!",
                    text: "Absolutely love this! The quality is amazing and it fits perfectly. The material feels premium and the colors are vibrant. Highly recommend!",
                    verified: true,
                    helpful: 12,
                    replies: [
                        {
                            name: "The Project Faith Team",
                            date: "2024-01-16",
                            text: "Thank you so much for your wonderful review, Sarah! We're thrilled you love your purchase! 💜"
                        }
                    ]
                },
                {
                    name: "Jessica L.",
                    rating: 4,
                    date: "2024-01-10",
                    title: "Great Quality",
                    text: "Great product overall. The material feels premium and the design is exactly as shown. Only minor issue is that it runs slightly small, so consider sizing up.",
                    verified: true,
                    helpful: 8,
                    replies: []
                },
                {
                    name: "Emily R.",
                    rating: 5,
                    date: "2024-01-05",
                    title: "Perfect for Summer!",
                    text: "Perfect for summer! Very comfortable and stylish. The fabric is breathable and the fit is flattering. Will definitely buy again in other colors!",
                    verified: false,
                    helpful: 15,
                    replies: []
                },
                {
                    name: "Michael K.",
                    rating: 4,
                    date: "2024-01-02",
                    title: "Good Value",
                    text: "Good value for money. The quality is solid and it looks exactly like the pictures. Shipping was fast and packaging was excellent.",
                    verified: true,
                    helpful: 6,
                    replies: []
                },
                {
                    name: "Amanda T.",
                    rating: 5,
                    date: "2023-12-28",
                    title: "Exceeded Expectations",
                    text: "This exceeded my expectations! The attention to detail is impressive and it's become one of my favorite pieces. Customer service was also very helpful.",
                    verified: true,
                    helpful: 9,
                    replies: []
                },
                {
                    name: "David P.",
                    rating: 3,
                    date: "2023-12-20",
                    title: "Decent but not perfect",
                    text: "It's decent but not perfect. The quality is okay for the price, but I expected a bit more based on the reviews. It's not bad, just not exceptional.",
                    verified: false,
                    helpful: 3,
                    replies: []
                }
            ];

            // Return a subset based on product category and rating
            return reviewTemplates.slice(0, Math.min(6, Math.floor(product.reviews / 20)));
        }

        function displayReviews(reviews) {
            const reviewsList = document.getElementById('reviewsList');
            reviewsList.innerHTML = '';

            if (reviews.length === 0) {
                reviewsList.innerHTML = '<p style="text-align: center; color: #666; padding: 2rem;">No reviews match your current filter.</p>';
                return;
            }

            reviews.forEach((review, index) => {
                const reviewElement = document.createElement('div');
                reviewElement.className = 'review-item';
                reviewElement.innerHTML = `
                    <div class="review-header">
                        <div class="reviewer-info">
                            <div class="reviewer-avatar">${review.name.charAt(0)}</div>
                            <div class="reviewer-details">
                                <span class="reviewer-name">${review.name}</span>
                                <span class="review-date">${new Date(review.date).toLocaleDateString()}</span>
                            </div>
                            ${review.verified ? '<span class="verified-badge">Verified Purchase</span>' : ''}
                        </div>
                    </div>
                    <div class="review-rating">${generateStars(review.rating)}</div>
                    <div class="review-title">${review.title}</div>
                    <div class="review-text">${review.text}</div>
                    <div class="review-actions">
                        <button class="review-action-btn helpful-btn" data-review-index="${index}">
                            <i class="fas fa-thumbs-up"></i>
                            Helpful (${review.helpful})
                        </button>
                        <button class="review-action-btn reply-btn" data-review-index="${index}">
                            <i class="fas fa-reply"></i>
                            Reply
                        </button>
                    </div>
                    <div class="reply-section">
                        <div class="reply-form" id="replyForm${index}">
                            <textarea class="reply-input" placeholder="Write a reply..."></textarea>
                            <div class="reply-buttons">
                                <button class="reply-submit" data-review-index="${index}">Post Reply</button>
                                <button class="reply-cancel" data-review-index="${index}">Cancel</button>
                            </div>
                        </div>
                        ${review.replies.length > 0 ? `
                            <div class="replies-list">
                                ${review.replies.map(reply => `
                                    <div class="reply-item">
                                        <div class="reviewer-info">
                                            <div class="reviewer-avatar">${reply.name.charAt(0)}</div>
                                            <div class="reviewer-details">
                                                <span class="reviewer-name">${reply.name}</span>
                                                <span class="review-date">${new Date(reply.date).toLocaleDateString()}</span>
                                            </div>
                                        </div>
                                        <div class="review-text">${reply.text}</div>
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                `;
                reviewsList.appendChild(reviewElement);
            });

            // Add event listeners for review interactions
            initializeReviewInteractions();
        }

        function initializeReviewForm() {
            const starRating = document.getElementById('starRating');
            const ratingText = document.getElementById('ratingText');
            const submitBtn = document.getElementById('submitReview');

            // Star rating functionality
            starRating.addEventListener('click', (e) => {
                if (e.target.classList.contains('star')) {
                    const rating = parseInt(e.target.getAttribute('data-rating'));
                    currentUserRating = rating;

                    // Update star display
                    const stars = starRating.querySelectorAll('.star');
                    stars.forEach((star, index) => {
                        if (index < rating) {
                            star.classList.add('active');
                        } else {
                            star.classList.remove('active');
                        }
                    });

                    // Update rating text
                    const ratingTexts = ['', 'Poor', 'Fair', 'Good', 'Very Good', 'Excellent'];
                    ratingText.textContent = ratingTexts[rating];
                }
            });

            // Submit review functionality
            submitBtn.addEventListener('click', (e) => {
                e.preventDefault();
                submitReview();
            });
        }

        function submitReview() {
            const name = document.getElementById('reviewerName').value.trim();
            const title = document.getElementById('reviewTitle').value.trim();
            const text = document.getElementById('reviewText').value.trim();
            const verified = document.getElementById('verifiedPurchase').checked;

            // Validation
            if (!name || !title || !text || currentUserRating === 0) {
                showNotification('Please fill in all fields and select a rating', 'warning');
                return;
            }

            // Simulate authentication check (in a real app, this would check if user is logged in)
            if (name.toLowerCase().includes('test') || name.toLowerCase().includes('spam')) {
                showNotification('Please enter a valid name', 'error');
                return;
            }

            // Create new review
            const newReview = {
                name: name,
                rating: currentUserRating,
                date: new Date().toISOString().split('T')[0],
                title: title,
                text: text,
                verified: verified,
                helpful: 0,
                replies: []
            };

            // Add to reviews array
            allReviews.unshift(newReview);

            // Clear form
            document.getElementById('reviewerName').value = '';
            document.getElementById('reviewTitle').value = '';
            document.getElementById('reviewText').value = '';
            document.getElementById('verifiedPurchase').checked = false;
            currentUserRating = 0;

            // Reset star rating
            document.querySelectorAll('.star').forEach(star => star.classList.remove('active'));
            document.getElementById('ratingText').textContent = 'Click to rate';

            // Refresh display
            displayReviews(allReviews);

            // Show success message
            showNotification('Thank you for your review! It has been posted successfully.', 'success');
        }

        function initializeReviewControls() {
            const filterSelect = document.getElementById('filterRating');
            const sortSelect = document.getElementById('sortReviews');

            filterSelect.addEventListener('change', () => {
                filterAndSortReviews();
            });

            sortSelect.addEventListener('change', () => {
                filterAndSortReviews();
            });
        }

        function filterAndSortReviews() {
            const filterValue = document.getElementById('filterRating').value;
            const sortValue = document.getElementById('sortReviews').value;

            let filteredReviews = [...allReviews];

            // Apply filter
            if (filterValue !== 'all') {
                const targetRating = parseInt(filterValue);
                filteredReviews = filteredReviews.filter(review => review.rating === targetRating);
            }

            // Apply sort
            switch (sortValue) {
                case 'newest':
                    filteredReviews.sort((a, b) => new Date(b.date) - new Date(a.date));
                    break;
                case 'oldest':
                    filteredReviews.sort((a, b) => new Date(a.date) - new Date(b.date));
                    break;
                case 'highest':
                    filteredReviews.sort((a, b) => b.rating - a.rating);
                    break;
                case 'lowest':
                    filteredReviews.sort((a, b) => a.rating - b.rating);
                    break;
                case 'helpful':
                    filteredReviews.sort((a, b) => b.helpful - a.helpful);
                    break;
            }

            displayReviews(filteredReviews);
        }

        function initializeReviewInteractions() {
            // Helpful button functionality
            document.querySelectorAll('.helpful-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const reviewIndex = parseInt(e.target.closest('.helpful-btn').getAttribute('data-review-index'));

                    if (!btn.classList.contains('active')) {
                        allReviews[reviewIndex].helpful++;
                        btn.classList.add('active');
                        btn.innerHTML = `<i class="fas fa-thumbs-up"></i> Helpful (${allReviews[reviewIndex].helpful})`;
                        showNotification('Thank you for your feedback!', 'success');
                    }
                });
            });

            // Reply button functionality
            document.querySelectorAll('.reply-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const reviewIndex = parseInt(e.target.closest('.reply-btn').getAttribute('data-review-index'));
                    const replyForm = document.getElementById(`replyForm${reviewIndex}`);
                    replyForm.classList.toggle('active');
                });
            });

            // Reply submit functionality
            document.querySelectorAll('.reply-submit').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const reviewIndex = parseInt(e.target.getAttribute('data-review-index'));
                    const replyForm = document.getElementById(`replyForm${reviewIndex}`);
                    const replyText = replyForm.querySelector('.reply-input').value.trim();

                    if (replyText) {
                        const newReply = {
                            name: "Anonymous User", // In a real app, this would be the logged-in user
                            date: new Date().toISOString().split('T')[0],
                            text: replyText
                        };

                        allReviews[reviewIndex].replies.push(newReply);
                        replyForm.querySelector('.reply-input').value = '';
                        replyForm.classList.remove('active');

                        displayReviews(allReviews);
                        showNotification('Reply posted successfully!', 'success');
                    }
                });
            });

            // Reply cancel functionality
            document.querySelectorAll('.reply-cancel').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const reviewIndex = parseInt(e.target.getAttribute('data-review-index'));
                    const replyForm = document.getElementById(`replyForm${reviewIndex}`);
                    replyForm.querySelector('.reply-input').value = '';
                    replyForm.classList.remove('active');
                });
            });
        }

        function loadRelatedProducts() {
            // Get all available products (hardcoded + localStorage)
            const allProducts = Object.values(getAllProducts());

            // Filter out the current product and get products from same category
            let relatedProducts = allProducts.filter(product =>
                String(product.id) !== String(currentProduct.id) &&
                product.category === currentProduct.category
            );

            // If we don't have enough products from same category, add some from other categories
            if (relatedProducts.length < 4) {
                const otherProducts = allProducts.filter(product =>
                    String(product.id) !== String(currentProduct.id) &&
                    product.category !== currentProduct.category
                );
                relatedProducts = [...relatedProducts, ...otherProducts];
            }

            // Limit to 4 products and shuffle for variety
            relatedProducts = shuffleArray(relatedProducts).slice(0, 4);

            const container = document.getElementById('relatedProducts');
            container.innerHTML = '';

            if (relatedProducts.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">No related products found.</p>';
                return;
            }

            relatedProducts.forEach(product => {
                const productCard = createRelatedProductCard(product);
                container.appendChild(productCard);
            });
        }

        // Shuffle array function for variety in related products
        function shuffleArray(array) {
            const shuffled = [...array];
            for (let i = shuffled.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            return shuffled;
        }

        // Create related product card with enhanced functionality
        function createRelatedProductCard(product) {
            const card = document.createElement('div');
            card.className = 'related-product-card';

            // Calculate discount if applicable
            const discount = product.originalPrice ?
                Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100) : 0;

            card.innerHTML = `
                <div class="related-product-image">
                    <img src="${product.images ? product.images[0] : product.image}" alt="${product.title}" loading="lazy">
                    <div class="related-product-overlay">
                        <button class="related-action-btn favorite-btn" data-product-id="${product.id}" title="Add to Favorites">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="related-action-btn view-btn" data-product-id="${product.id}" title="View Product">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    ${discount > 0 ? `<div class="related-discount-badge">-${discount}%</div>` : ''}
                </div>
                <div class="related-product-info">
                    <h4 class="related-product-title">${product.title}</h4>
                    <div class="related-product-price">
                        <span class="related-current-price">$${product.price}</span>
                        ${product.originalPrice ? `<span class="related-original-price">$${product.originalPrice}</span>` : ''}
                    </div>
                    <div class="related-product-rating">
                        <div class="stars">
                            ${generateStars(product.rating)}
                        </div>
                        <span class="rating-count">(${product.reviews})</span>
                    </div>
                    <button class="btn btn-primary related-add-to-favorites" data-product-id="${product.id}">
                        <i class="far fa-heart"></i>
                        Add to Favorites
                    </button>
                </div>
            `;

            // Add event listeners
            const favoriteBtn = card.querySelector('.favorite-btn');
            const viewBtn = card.querySelector('.view-btn');
            const addToFavoritesBtn = card.querySelector('.related-add-to-favorites');

            favoriteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(product.id, product.title);
                updateRelatedFavoriteButton(favoriteBtn, product.id);
            });

            viewBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                window.location.href = `product.html?id=${product.id}`;
            });

            addToFavoritesBtn.addEventListener('click', (e) => {
                e.stopPropagation();

                // Add visual feedback
                addToFavoritesBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    addToFavoritesBtn.style.transform = '';
                }, 150);

                toggleFavorite(product.id, product.title);
                updateRelatedFavoriteButton(favoriteBtn, product.id);
            });

            // Navigate to product on card click
            card.addEventListener('click', (e) => {
                if (!e.target.closest('button')) {
                    window.location.href = `product.html?id=${product.id}`;
                }
            });

            // Update favorite button state
            updateRelatedFavoriteButton(favoriteBtn, product.id);

            return card;
        }

        // Update favorite button state for related products
        function updateRelatedFavoriteButton(button, productId) {
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            const icon = button.querySelector('i');

            if (favorites.includes(productId)) {
                icon.className = 'fas fa-heart';
                button.style.color = '#4B0082';
            } else {
                icon.className = 'far fa-heart';
                button.style.color = '';
            }
        }

        function initializeProductEvents() {
            // Quantity controls
            document.getElementById('decreaseQty').addEventListener('click', () => {
                const qtyInput = document.getElementById('quantity');
                const currentQty = parseInt(qtyInput.value);
                if (currentQty > 1) {
                    qtyInput.value = currentQty - 1;
                }
            });

            document.getElementById('increaseQty').addEventListener('click', () => {
                const qtyInput = document.getElementById('quantity');
                const currentQty = parseInt(qtyInput.value);
                const maxQty = parseInt(qtyInput.max);
                if (currentQty < maxQty) {
                    qtyInput.value = currentQty + 1;
                }
            });

            // Add to cart
            document.getElementById('addToCartBtn').addEventListener('click', () => {
                if (currentProduct.sizes && currentProduct.sizes.length > 0 && !selectedSize) {
                    showNotification('Please select a size', 'warning');
                    return;
                }

                const quantity = parseInt(document.getElementById('quantity').value);
                const productToAdd = {
                    id: currentProduct.id,
                    title: currentProduct.title,
                    price: currentProduct.price,
                    image: currentProduct.images[0], // Use first image
                    quantity: quantity,
                    selectedSize: selectedSize
                };

                // Add visual feedback
                const addToCartBtn = document.getElementById('addToCartBtn');
                addToCartBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    addToCartBtn.style.transform = '';
                }, 150);

                // Add to cart using existing cart functionality
                addToCart(productToAdd);

                // Show success notification with details
                let message = `${currentProduct.title} added to cart!`;
                if (selectedSize) message += ` (Size: ${selectedSize})`;
                if (quantity > 1) message += ` (Qty: ${quantity})`;

                showNotification(message, 'success');

                // Update cart count
                updateCartCount();
            });

            // Favorite button
            document.getElementById('favoriteBtn').addEventListener('click', () => {
                const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
                const isCurrentlyFavorite = favorites.includes(currentProduct.id);

                // Add visual feedback
                const favoriteBtn = document.getElementById('favoriteBtn');
                favoriteBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    favoriteBtn.style.transform = '';
                }, 150);

                toggleFavorite(currentProduct.id);
                updateFavoriteButton(currentProduct.id);

                // Show appropriate notification
                if (isCurrentlyFavorite) {
                    showNotification(`${currentProduct.title} removed from favorites`, 'success');
                } else {
                    showNotification(`${currentProduct.title} added to favorites!`, 'success');
                }
            });



            // Secondary Add to Cart button
            document.getElementById('addToCartSecondaryBtn').addEventListener('click', () => {
                if (currentProduct.sizes && currentProduct.sizes.length > 0 && !selectedSize) {
                    showNotification('Please select a size', 'warning');
                    return;
                }

                const quantity = parseInt(document.getElementById('quantity').value);
                const productToAdd = {
                    id: currentProduct.id,
                    title: currentProduct.title,
                    price: currentProduct.price,
                    image: currentProduct.images[0],
                    quantity: quantity,
                    selectedSize: selectedSize
                };

                // Add visual feedback
                const addToCartSecondaryBtn = document.getElementById('addToCartSecondaryBtn');
                addToCartSecondaryBtn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    addToCartSecondaryBtn.style.transform = '';
                }, 150);

                // Add to cart using existing cart functionality
                addToCart(productToAdd);
                showNotification(`${currentProduct.title} added to cart!`, 'success');
                updateCartCount();
            });

            // Share button
            document.getElementById('shareBtn').addEventListener('click', () => {
                if (navigator.share) {
                    navigator.share({
                        title: currentProduct.title,
                        text: `Check out this ${currentProduct.title} for $${currentProduct.price}`,
                        url: window.location.href
                    }).then(() => {
                        showNotification('Product shared successfully!', 'success');
                    }).catch(() => {
                        fallbackShare();
                    });
                } else {
                    fallbackShare();
                }
            });

            // Large Favorites button
            document.getElementById('favoriteBtnLarge').addEventListener('click', () => {
                const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
                const isCurrentlyFavorite = favorites.includes(currentProduct.id);

                // Add visual feedback
                const favoriteBtnLarge = document.getElementById('favoriteBtnLarge');
                favoriteBtnLarge.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    favoriteBtnLarge.style.transform = '';
                }, 150);

                toggleFavorite(currentProduct.id);
                updateFavoriteButton(currentProduct.id);
                updateLargeFavoriteButton(currentProduct.id);

                // Show appropriate notification
                if (isCurrentlyFavorite) {
                    showNotification(`${currentProduct.title} removed from favorites`, 'success');
                } else {
                    showNotification(`${currentProduct.title} added to favorites!`, 'success');
                }
            });

            // Initialize user rating functionality
            initializeUserRating();
        }

        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');

                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }



        function updateFavoriteButton(productId) {
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            const favoriteBtn = document.getElementById('favoriteBtn');
            const icon = favoriteBtn.querySelector('i');
            const text = favoriteBtn.querySelector('.favorite-text');

            if (favorites.includes(productId)) {
                favoriteBtn.classList.add('active');
                icon.className = 'fas fa-heart';
                if (text) text.textContent = 'Remove from Favorites';
                favoriteBtn.title = 'Remove from Favorites';
            } else {
                favoriteBtn.classList.remove('active');
                icon.className = 'far fa-heart';
                if (text) text.textContent = 'Add to Favorites';
                favoriteBtn.title = 'Add to Favorites';
            }
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fas fa-star"></i>';
            }

            if (hasHalfStar) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            }

            const emptyStars = 5 - Math.ceil(rating);
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="far fa-star"></i>';
            }

            return stars;
        }

        // Utility functions for cart and favorites
        function updateCartCount() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const cartCount = document.getElementById('cartCount');
            if (cartCount) {
                cartCount.textContent = cart.reduce((total, item) => total + item.quantity, 0);
            }
        }

        function updateFavoritesCount() {
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            const favoritesCount = document.getElementById('favoritesCount');
            if (favoritesCount) {
                favoritesCount.textContent = favorites.length;
            }
        }

        // Add to cart function
        function addToCart(product) {
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            const existingItem = cart.find(item =>
                item.id === product.id &&
                item.selectedSize === product.selectedSize
            );

            if (existingItem) {
                existingItem.quantity += product.quantity;
            } else {
                cart.push(product);
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
        }

        // Toggle favorite function
        function toggleFavorite(productId) {
            let favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            const index = favorites.indexOf(productId);

            if (index > -1) {
                favorites.splice(index, 1);
            } else {
                favorites.push(productId);
            }

            localStorage.setItem('favorites', JSON.stringify(favorites));
            updateFavoritesCount();
        }

        // Large favorites button update function
        function updateLargeFavoriteButton(productId) {
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            const favoriteBtnLarge = document.getElementById('favoriteBtnLarge');
            const icon = favoriteBtnLarge.querySelector('i');
            const text = favoriteBtnLarge.querySelector('.favorites-text');

            if (favorites.includes(productId)) {
                favoriteBtnLarge.classList.add('active');
                icon.className = 'fas fa-heart';
                if (text) text.textContent = 'Remove from Favorites';
                favoriteBtnLarge.title = 'Remove from Favorites';
            } else {
                favoriteBtnLarge.classList.remove('active');
                icon.className = 'far fa-heart';
                if (text) text.textContent = 'Add to Favorites';
                favoriteBtnLarge.title = 'Add to Favorites';
            }
        }

        // User rating functionality
        function initializeUserRating() {
            const userStars = document.querySelectorAll('.user-star');
            const userRatingText = document.getElementById('userRatingText');
            const ratingActions = document.getElementById('ratingActions');
            const submitRatingBtn = document.getElementById('submitRatingBtn');
            const cancelRatingBtn = document.getElementById('cancelRatingBtn');
            const userRatingStatus = document.getElementById('userRatingStatus');
            let selectedRating = 0;

            // Check if user has already rated this product
            const existingRating = getUserRating(currentProduct.id);
            if (existingRating) {
                showRatingStatus();
                return;
            }

            // Star hover and click events
            userStars.forEach((star, index) => {
                const rating = index + 1;

                star.addEventListener('mouseenter', () => {
                    highlightStars(rating);
                    userRatingText.textContent = getRatingText(rating);
                });

                star.addEventListener('click', () => {
                    selectedRating = rating;
                    setActiveStars(rating);
                    userRatingText.textContent = `You rated: ${getRatingText(rating)}`;
                    ratingActions.style.display = 'block';
                });
            });

            // Reset stars on mouse leave
            document.getElementById('userStarRating').addEventListener('mouseleave', () => {
                if (selectedRating === 0) {
                    resetStars();
                    userRatingText.textContent = 'Click to rate';
                } else {
                    setActiveStars(selectedRating);
                    userRatingText.textContent = `You rated: ${getRatingText(selectedRating)}`;
                }
            });

            // Submit rating
            submitRatingBtn.addEventListener('click', () => {
                if (selectedRating > 0) {
                    const reviewText = document.getElementById('quickReviewText').value.trim();
                    submitUserRating(currentProduct.id, selectedRating, reviewText);
                    showRatingStatus();
                    ratingActions.style.display = 'none';
                    showNotification('Thank you for rating this product!', 'success');
                }
            });

            // Cancel rating
            cancelRatingBtn.addEventListener('click', () => {
                selectedRating = 0;
                resetStars();
                userRatingText.textContent = 'Click to rate';
                ratingActions.style.display = 'none';
                document.getElementById('quickReviewText').value = '';
            });
        }

        // Share functionality
        function fallbackShare() {
            const url = window.location.href;
            const title = currentProduct.title;
            const text = `Check out this ${title} for $${currentProduct.price}`;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(`${text} - ${url}`).then(() => {
                    showNotification('Product link copied to clipboard!', 'success');
                }).catch(() => {
                    showShareModal();
                });
            } else {
                showShareModal();
            }
        }

        function showShareModal() {
            const shareText = `Check out this ${currentProduct.title} for $${currentProduct.price} - ${window.location.href}`;
            const modal = document.createElement('div');
            modal.className = 'share-modal';
            modal.innerHTML = `
                <div class="share-modal-content">
                    <h3>Share this product</h3>
                    <textarea readonly>${shareText}</textarea>
                    <div class="share-buttons">
                        <button onclick="this.parentElement.parentElement.parentElement.remove()">Close</button>
                        <button onclick="navigator.clipboard.writeText('${shareText.replace(/'/g, "\\'")}').then(() => { showNotification('Copied!', 'success'); this.parentElement.parentElement.parentElement.remove(); })">Copy</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);

            // Add styles for the modal
            if (!document.getElementById('share-modal-styles')) {
                const style = document.createElement('style');
                style.id = 'share-modal-styles';
                style.textContent = `
                    .share-modal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.5);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 10000;
                    }
                    .share-modal-content {
                        background: white;
                        padding: 2rem;
                        border-radius: 10px;
                        max-width: 400px;
                        width: 90%;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    }
                    .share-modal h3 {
                        margin-top: 0;
                        color: #333;
                    }
                    .share-modal textarea {
                        width: 100%;
                        height: 80px;
                        margin: 1rem 0;
                        padding: 0.5rem;
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        resize: none;
                        font-family: inherit;
                    }
                    .share-buttons {
                        display: flex;
                        gap: 1rem;
                        justify-content: flex-end;
                    }
                    .share-buttons button {
                        padding: 0.5rem 1rem;
                        border: 1px solid #ddd;
                        border-radius: 5px;
                        cursor: pointer;
                        background: white;
                        transition: all 0.3s ease;
                    }
                    .share-buttons button:hover {
                        background: #f5f5f5;
                        border-color: #4B0082;
                    }
                    .share-buttons button:last-child {
                        background: #4B0082;
                        color: white;
                        border-color: #4B0082;
                    }
                    .share-buttons button:last-child:hover {
                        background: #5a0099;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // User rating helper functions
        function highlightStars(rating) {
            const userStars = document.querySelectorAll('.user-star');
            userStars.forEach((star, index) => {
                if (index < rating) {
                    star.classList.add('hover-effect');
                } else {
                    star.classList.remove('hover-effect');
                }
            });
        }

        function setActiveStars(rating) {
            const userStars = document.querySelectorAll('.user-star');
            userStars.forEach((star, index) => {
                star.classList.remove('hover-effect');
                if (index < rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        function resetStars() {
            const userStars = document.querySelectorAll('.user-star');
            userStars.forEach(star => {
                star.classList.remove('active', 'hover-effect');
            });
        }

        function getRatingText(rating) {
            const ratingTexts = {
                1: '1 star - Poor',
                2: '2 stars - Fair',
                3: '3 stars - Good',
                4: '4 stars - Very Good',
                5: '5 stars - Excellent'
            };
            return ratingTexts[rating] || 'Click to rate';
        }

        function submitUserRating(productId, rating, reviewText) {
            const userRatings = JSON.parse(localStorage.getItem('userRatings')) || {};
            userRatings[productId] = {
                rating: rating,
                review: reviewText,
                date: new Date().toISOString()
            };
            localStorage.setItem('userRatings', JSON.stringify(userRatings));
        }

        function getUserRating(productId) {
            const userRatings = JSON.parse(localStorage.getItem('userRatings')) || {};
            return userRatings[productId] || null;
        }

        function showRatingStatus() {
            const userRatingContainer = document.querySelector('.user-rating-container');
            const userRatingStatus = document.getElementById('userRatingStatus');
            const ratingActions = document.getElementById('ratingActions');

            userRatingContainer.style.display = 'none';
            ratingActions.style.display = 'none';
            userRatingStatus.style.display = 'flex';
        }
    </script>
</body>
</html>