<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP Access Test - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 2rem;
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
        }
        .test-button {
            background: #4B0082;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #8A2BE2;
            transform: translateY(-2px);
        }
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-crown" style="color: #FFD700;"></i> VIP Access Control Test</h1>
        <p>This page allows you to test the VIP access control system with different user types.</p>

        <div class="test-section">
            <h3>Current User Status</h3>
            <div id="currentStatus" class="status info">
                <i class="fas fa-info-circle"></i> Loading user status...
            </div>
        </div>

        <div class="test-section">
            <h3>Login as Different Users</h3>
            <p>Test the VIP access system by logging in as different user types:</p>
            
            <button class="test-button" onclick="loginAsGuest()">
                <i class="fas fa-user"></i> Test as Guest (No Login)
            </button>
            
            <button class="test-button" onclick="loginAsUser()">
                <i class="fas fa-user"></i> Login as Regular User
            </button>
            
            <button class="test-button" onclick="loginAsVIP()">
                <i class="fas fa-crown"></i> Login as VIP User
            </button>
            
            <button class="test-button" onclick="loginAsAdmin()">
                <i class="fas fa-user-shield"></i> Login as Admin
            </button>
        </div>

        <div class="test-section">
            <h3>Test Sale Page Access</h3>
            <p>Click the button below to test access to the sale page with your current user status:</p>
            
            <button class="test-button" onclick="testSaleAccess()">
                <i class="fas fa-shopping-bag"></i> Test Sale Page Access
            </button>
            
            <button class="test-button" onclick="openSalePage()">
                <i class="fas fa-external-link-alt"></i> Open Sale Page
            </button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults" class="status info">
                <i class="fas fa-info-circle"></i> No tests run yet. Use the buttons above to test different scenarios.
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/auth.js"></script>
    <script src="js/vip-access.js"></script>
    
    <script>
        // Initialize auth manager
        const authManager = new AuthManager();
        
        function updateStatus() {
            const statusDiv = document.getElementById('currentStatus');
            const accessLevel = VIPAccessControl.getUserAccessLevel();
            const user = authManager.getCurrentUser();
            
            let statusHTML = '';
            let statusClass = 'info';
            
            if (accessLevel === 'guest') {
                statusHTML = '<i class="fas fa-user"></i> Not logged in (Guest)';
                statusClass = 'error';
            } else if (accessLevel === 'user') {
                statusHTML = `<i class="fas fa-user"></i> Logged in as Regular User: ${user.firstName} ${user.lastName}`;
                statusClass = 'error';
            } else if (accessLevel === 'vip') {
                statusHTML = `<i class="fas fa-crown"></i> Logged in as VIP User: ${user.firstName} ${user.lastName}`;
                statusClass = 'success';
            } else if (accessLevel === 'admin') {
                statusHTML = `<i class="fas fa-user-shield"></i> Logged in as Admin: ${user.firstName} ${user.lastName}`;
                statusClass = 'success';
            }
            
            statusDiv.innerHTML = statusHTML;
            statusDiv.className = `status ${statusClass}`;
        }
        
        function loginAsGuest() {
            authManager.logout();
            updateStatus();
            showResult('Logged out. You are now a guest user.', 'info');
        }
        
        async function loginAsUser() {
            try {
                await authManager.login('<EMAIL>', 'user123');
                updateStatus();
                showResult('Successfully logged in as regular user.', 'success');
            } catch (error) {
                showResult('Failed to login as regular user: ' + error.message, 'error');
            }
        }
        
        async function loginAsVIP() {
            try {
                await authManager.login('<EMAIL>', 'vip123');
                updateStatus();
                showResult('Successfully logged in as VIP user.', 'success');
            } catch (error) {
                showResult('Failed to login as VIP user: ' + error.message, 'error');
            }
        }
        
        async function loginAsAdmin() {
            try {
                await authManager.login('<EMAIL>', 'admin123');
                updateStatus();
                showResult('Successfully logged in as admin.', 'success');
            } catch (error) {
                showResult('Failed to login as admin: ' + error.message, 'error');
            }
        }
        
        function testSaleAccess() {
            const hasAccess = VIPAccessControl.checkVIPAccess();
            const accessLevel = VIPAccessControl.getUserAccessLevel();
            
            if (hasAccess) {
                showResult(`✅ Access GRANTED! You have ${accessLevel.toUpperCase()} access to the sale page.`, 'success');
            } else {
                showResult(`❌ Access DENIED! ${accessLevel.toUpperCase()} users cannot access the VIP sale page.`, 'error');
            }
        }
        
        function openSalePage() {
            window.open('sale.html', '_blank');
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `<i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle'}"></i> ${message}`;
            resultsDiv.className = `status ${type}`;
        }
        
        // Initialize status on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus();
        });
    </script>
</body>
</html>
