<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Category Cleanup - VAITH Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .warning-box {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success-box {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        .btn {
            background: #4B0082;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #3a0066;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
        .product-item {
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Category Cleanup Tool</h1>
        
        <div class="info-box">
            <h3>📋 What this tool does:</h3>
            <p>This tool will scan your product database and remove or update any products that have "shoes" or "accessories" categories, as these categories have been removed from the website.</p>
        </div>

        <div class="warning-box">
            <h3>⚠️ Important:</h3>
            <p><strong>This action cannot be undone!</strong> Products with "shoes" or "accessories" categories will be:</p>
            <ul>
                <li><strong>Option 1:</strong> Deleted completely</li>
                <li><strong>Option 2:</strong> Moved to "women" or "men" category (you choose)</li>
            </ul>
            <p>Make sure to backup your data if needed before proceeding.</p>
        </div>

        <div class="success-box" id="successBox">
            <h3>✅ Cleanup Complete!</h3>
            <p id="successMessage"></p>
        </div>

        <div style="text-align: center;">
            <button class="btn" onclick="scanProducts()">🔍 Scan for Products</button>
            <button class="btn btn-danger" onclick="deleteProducts()" id="deleteBtn" style="display: none;">🗑️ Delete These Products</button>
            <button class="btn" onclick="moveToWomen()" id="moveWomenBtn" style="display: none;">👗 Move to Women</button>
            <button class="btn" onclick="moveToMen()" id="moveMenBtn" style="display: none;">👔 Move to Men</button>
        </div>

        <div class="results" id="results">
            <h3>Found Products:</h3>
            <div id="productList"></div>
        </div>
    </div>

    <script>
        let problematicProducts = [];

        function scanProducts() {
            try {
                const storedProducts = localStorage.getItem('vaith_products');
                if (!storedProducts) {
                    alert('No products found in database.');
                    return;
                }

                const products = JSON.parse(storedProducts);
                problematicProducts = products.filter(product => 
                    product.category === 'shoes' || product.category === 'accessories'
                );

                if (problematicProducts.length === 0) {
                    document.getElementById('successBox').style.display = 'block';
                    document.getElementById('successMessage').textContent = 
                        `✅ Great! No products found with "shoes" or "accessories" categories. Your database is clean!`;
                    return;
                }

                // Show results
                const resultsDiv = document.getElementById('results');
                const productListDiv = document.getElementById('productList');
                
                productListDiv.innerHTML = problematicProducts.map(product => `
                    <div class="product-item">
                        <strong>${product.name}</strong><br>
                        <small>Category: ${product.category} | SKU: ${product.sku} | Price: $${product.price}</small>
                    </div>
                `).join('');

                resultsDiv.style.display = 'block';
                document.getElementById('deleteBtn').style.display = 'inline-block';
                document.getElementById('moveWomenBtn').style.display = 'inline-block';
                document.getElementById('moveMenBtn').style.display = 'inline-block';

            } catch (error) {
                console.error('Error scanning products:', error);
                alert('Error scanning products. Check console for details.');
            }
        }

        function deleteProducts() {
            if (!confirm(`Are you sure you want to DELETE ${problematicProducts.length} products? This cannot be undone!`)) {
                return;
            }

            try {
                const storedProducts = localStorage.getItem('vaith_products');
                const products = JSON.parse(storedProducts);
                
                const cleanProducts = products.filter(product => 
                    product.category !== 'shoes' && product.category !== 'accessories'
                );

                localStorage.setItem('vaith_products', JSON.stringify(cleanProducts));

                document.getElementById('successBox').style.display = 'block';
                document.getElementById('successMessage').textContent = 
                    `✅ Successfully deleted ${problematicProducts.length} products with "shoes" or "accessories" categories.`;
                
                hideButtons();
                document.getElementById('results').style.display = 'none';

            } catch (error) {
                console.error('Error deleting products:', error);
                alert('Error deleting products. Check console for details.');
            }
        }

        function moveToWomen() {
            moveProducts('women');
        }

        function moveToMen() {
            moveProducts('men');
        }

        function moveProducts(newCategory) {
            if (!confirm(`Are you sure you want to move ${problematicProducts.length} products to "${newCategory}" category?`)) {
                return;
            }

            try {
                const storedProducts = localStorage.getItem('vaith_products');
                const products = JSON.parse(storedProducts);
                
                const updatedProducts = products.map(product => {
                    if (product.category === 'shoes' || product.category === 'accessories') {
                        return {
                            ...product,
                            category: newCategory,
                            updatedDate: new Date().toISOString()
                        };
                    }
                    return product;
                });

                localStorage.setItem('vaith_products', JSON.stringify(updatedProducts));

                document.getElementById('successBox').style.display = 'block';
                document.getElementById('successMessage').textContent = 
                    `✅ Successfully moved ${problematicProducts.length} products to "${newCategory}" category.`;
                
                hideButtons();
                document.getElementById('results').style.display = 'none';

            } catch (error) {
                console.error('Error moving products:', error);
                alert('Error moving products. Check console for details.');
            }
        }

        function hideButtons() {
            document.getElementById('deleteBtn').style.display = 'none';
            document.getElementById('moveWomenBtn').style.display = 'none';
            document.getElementById('moveMenBtn').style.display = 'none';
        }
    </script>
</body>
</html>
