<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Product Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .product-item {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
        .test-link {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Product Details Fix Test</h1>
    
    <div class="test-section">
        <h2>Products in localStorage</h2>
        <div id="localStorageProducts">Loading...</div>
    </div>
    
    <div class="test-section">
        <h2>Test Product Links</h2>
        <div id="testLinks">Loading...</div>
    </div>
    
    <div class="test-section">
        <h2>Instructions</h2>
        <ol>
            <li>First, add a new product using the <a href="admin-products.html" target="_blank">Admin Panel</a></li>
            <li>Come back to this page and refresh to see the new product listed above</li>
            <li>Click on the test links to verify that the product details page opens correctly</li>
            <li>The product details should load without showing "Product not found" error</li>
        </ol>
    </div>

    <script>
        function loadTest() {
            // Check localStorage products
            const storedProducts = localStorage.getItem('vaith_products');
            const localStorageDiv = document.getElementById('localStorageProducts');
            const testLinksDiv = document.getElementById('testLinks');
            
            if (storedProducts) {
                try {
                    const products = JSON.parse(storedProducts);
                    if (products.length > 0) {
                        localStorageDiv.innerHTML = `<p class="success">Found ${products.length} products in localStorage:</p>`;
                        
                        let linksHTML = '';
                        products.forEach(product => {
                            localStorageDiv.innerHTML += `
                                <div class="product-item">
                                    <strong>ID:</strong> ${product.id}<br>
                                    <strong>Name:</strong> ${product.name}<br>
                                    <strong>Category:</strong> ${product.category}<br>
                                    <strong>Price:</strong> $${product.price}
                                </div>
                            `;
                            
                            linksHTML += `<a href="product.html?id=${product.id}" class="test-link" target="_blank">Test Product ${product.id}</a>`;
                        });
                        
                        testLinksDiv.innerHTML = linksHTML;
                    } else {
                        localStorageDiv.innerHTML = '<p class="error">No products found in localStorage. Please add some products first.</p>';
                        testLinksDiv.innerHTML = '<p>No test links available - add products first.</p>';
                    }
                } catch (error) {
                    localStorageDiv.innerHTML = `<p class="error">Error parsing localStorage: ${error.message}</p>`;
                    testLinksDiv.innerHTML = '<p>Error loading test links.</p>';
                }
            } else {
                localStorageDiv.innerHTML = '<p class="error">No products found in localStorage. Please add some products first.</p>';
                testLinksDiv.innerHTML = '<p>No test links available - add products first.</p>';
            }
            
            // Add hardcoded product test links
            testLinksDiv.innerHTML += '<br><br><strong>Hardcoded Product Tests:</strong><br>';
            testLinksDiv.innerHTML += '<a href="product.html?id=1" class="test-link" target="_blank">Test Hardcoded Product 1</a>';
            testLinksDiv.innerHTML += '<a href="product.html?id=2" class="test-link" target="_blank">Test Hardcoded Product 2</a>';
        }
        
        // Load test on page load
        document.addEventListener('DOMContentLoaded', loadTest);
        
        // Add refresh button functionality
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                setTimeout(loadTest, 100);
            }
        });
    </script>
</body>
</html>
