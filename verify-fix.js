// Verification script for product details fix
console.log('=== Product Details Fix Verification ===');

// Check if localStorage has products
const storedProducts = localStorage.getItem('vaith_products');
if (storedProducts) {
    try {
        const products = JSON.parse(storedProducts);
        console.log(`✓ Found ${products.length} products in localStorage`);
        
        products.forEach((product, index) => {
            console.log(`  Product ${index + 1}: ID=${product.id}, Name="${product.name}"`);
            
            // Test if product details URL would work
            const testUrl = `product.html?id=${product.id}`;
            console.log(`    Test URL: ${testUrl}`);
        });
        
        if (products.length > 0) {
            console.log('\n✓ Fix should work! Try opening these URLs:');
            products.forEach(product => {
                console.log(`   - product.html?id=${product.id} (${product.name})`);
            });
        }
    } catch (error) {
        console.error('✗ Error parsing localStorage products:', error);
    }
} else {
    console.log('✗ No products found in localStorage');
    console.log('  Please add some products using the admin panel first');
}

// Test the getAllProducts function if we're on the product page
if (typeof getAllProducts === 'function') {
    console.log('\n=== Testing getAllProducts function ===');
    const allProducts = getAllProducts();
    const productIds = Object.keys(allProducts);
    console.log(`✓ getAllProducts() returned ${productIds.length} products`);
    
    productIds.forEach(id => {
        const product = allProducts[id];
        console.log(`  ID: ${id} -> ${product.title}`);
    });
} else {
    console.log('\n⚠ getAllProducts function not available (not on product page)');
}

console.log('\n=== Verification Complete ===');
