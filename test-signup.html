<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 2rem;
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus {
            outline: none;
            border-color: #4B0082;
        }
        .test-button {
            background: #4B0082;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }
        .test-button:hover {
            background: #8A2BE2;
        }
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-user-plus"></i> Signup Test & Debug</h1>
        <p>Test the signup functionality and debug any issues.</p>

        <form id="testSignupForm">
            <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" value="Test" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" value="User" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="testpass123" required>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" id="confirmPassword" name="confirmPassword" value="testpass123" required>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="terms" name="terms" checked> 
                    I accept the terms and conditions
                </label>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="newsletter" name="newsletter"> 
                    Subscribe to newsletter
                </label>
            </div>
            
            <button type="submit" class="test-button">
                <i class="fas fa-user-plus"></i> Create Test Account
            </button>
        </form>

        <div id="status" class="status info">
            <i class="fas fa-info-circle"></i> Ready to test signup...
        </div>

        <div id="debugInfo" class="debug-info">
            Debug information will appear here...
        </div>

        <div style="margin-top: 2rem;">
            <h3>Quick Actions</h3>
            <button class="test-button" onclick="checkAuthManager()" style="margin: 0.5rem 0;">
                Check Auth Manager Status
            </button>
            <button class="test-button" onclick="listAllUsers()" style="margin: 0.5rem 0;">
                List All Users
            </button>
            <button class="test-button" onclick="clearLocalStorage()" style="margin: 0.5rem 0;">
                Clear Local Storage
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/auth.js"></script>
    
    <script>
        // Initialize auth manager
        let authManager;
        
        document.addEventListener('DOMContentLoaded', function() {
            try {
                authManager = new AuthManager();
                updateDebugInfo('Auth Manager initialized successfully');
                updateStatus('Auth Manager ready. You can test signup now.', 'success');
            } catch (error) {
                updateDebugInfo('Error initializing Auth Manager: ' + error.message);
                updateStatus('Error initializing Auth Manager: ' + error.message, 'error');
            }
        });

        document.getElementById('testSignupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            updateStatus('Processing signup...', 'info');
            updateDebugInfo('Starting signup process...');
            
            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value,
                terms: document.getElementById('terms').checked,
                newsletter: document.getElementById('newsletter').checked
            };
            
            updateDebugInfo('Form data collected: ' + JSON.stringify(formData, null, 2));
            
            // Basic validation
            if (formData.password !== formData.confirmPassword) {
                updateStatus('Passwords do not match!', 'error');
                return;
            }
            
            if (!formData.terms) {
                updateStatus('Please accept the terms and conditions!', 'error');
                return;
            }
            
            try {
                updateDebugInfo('Calling authManager.signup...');
                
                const user = await authManager.signup({
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    password: formData.password,
                    newsletter: formData.newsletter
                });
                
                updateDebugInfo('Signup successful! User created: ' + JSON.stringify(user, null, 2));
                updateStatus('Account created successfully! User ID: ' + user.id, 'success');
                
                // List all users to verify
                setTimeout(() => {
                    listAllUsers();
                }, 1000);
                
            } catch (error) {
                updateDebugInfo('Signup error: ' + error.message);
                updateStatus('Signup failed: ' + error.message, 'error');
            }
        });
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle';
            statusDiv.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
            statusDiv.className = `status ${type}`;
        }
        
        function updateDebugInfo(message) {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.textContent += `[${timestamp}] ${message}\n`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        function checkAuthManager() {
            try {
                updateDebugInfo('Checking Auth Manager...');
                updateDebugInfo('Auth Manager exists: ' + (typeof authManager !== 'undefined'));
                updateDebugInfo('Auth Manager constructor: ' + (typeof AuthManager !== 'undefined'));
                
                if (authManager) {
                    updateDebugInfo('Current user: ' + JSON.stringify(authManager.getCurrentUser()));
                    updateDebugInfo('Is logged in: ' + authManager.isLoggedIn());
                    updateDebugInfo('Total users: ' + authManager.getAllUsers().length);
                }
                
                updateStatus('Auth Manager check completed. See debug info.', 'info');
            } catch (error) {
                updateDebugInfo('Error checking Auth Manager: ' + error.message);
                updateStatus('Error checking Auth Manager: ' + error.message, 'error');
            }
        }
        
        function listAllUsers() {
            try {
                const users = authManager.getAllUsers();
                updateDebugInfo('All users in system:');
                users.forEach(user => {
                    updateDebugInfo(`- ${user.firstName} ${user.lastName} (${user.email}) - Role: ${user.role}`);
                });
                updateStatus(`Found ${users.length} users in system.`, 'info');
            } catch (error) {
                updateDebugInfo('Error listing users: ' + error.message);
                updateStatus('Error listing users: ' + error.message, 'error');
            }
        }
        
        function clearLocalStorage() {
            localStorage.clear();
            updateDebugInfo('Local storage cleared');
            updateStatus('Local storage cleared. Refresh page to reinitialize.', 'info');
        }
    </script>
</body>
</html>
