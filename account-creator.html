<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Creator - VAITH</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            padding: 2rem;
            font-family: 'Inter', sans-serif;
            background: #f8f9fa;
        }
        .creator-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        .account-type {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        .account-card {
            flex: 1;
            padding: 1.5rem;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .account-card:hover {
            border-color: #4B0082;
            transform: translateY(-2px);
        }
        .account-card.selected {
            border-color: #4B0082;
            background: rgba(75, 0, 130, 0.05);
        }
        .account-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .account-card.user .icon { color: #28a745; }
        .account-card.vip .icon { color: #FFD700; }
        .account-card.admin .icon { color: #dc3545; }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4B0082;
        }
        .create-btn {
            background: #4B0082;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }
        .create-btn:hover {
            background: #8A2BE2;
        }
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .users-list {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #ddd;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .user-role {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .user-role.user { background: #d4edda; color: #155724; }
        .user-role.vip { background: #fff3cd; color: #856404; }
        .user-role.admin { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="creator-container">
        <h1><i class="fas fa-users-cog"></i> Account Creator & Manager</h1>
        <p>Create new accounts with different roles and manage existing users.</p>

        <div class="account-type">
            <div class="account-card user" data-role="user">
                <div class="icon"><i class="fas fa-user"></i></div>
                <h3>Regular User</h3>
                <p>Standard account with basic access</p>
            </div>
            <div class="account-card vip selected" data-role="vip">
                <div class="icon"><i class="fas fa-crown"></i></div>
                <h3>VIP User</h3>
                <p>Premium account with VIP access</p>
            </div>
            <div class="account-card admin" data-role="admin">
                <div class="icon"><i class="fas fa-user-shield"></i></div>
                <h3>Admin</h3>
                <p>Full administrative access</p>
            </div>
        </div>

        <form id="createAccountForm">
            <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="password123" required>
            </div>
            
            <div class="form-group">
                <label for="role">Account Role</label>
                <select id="role" name="role">
                    <option value="user">Regular User</option>
                    <option value="vip" selected>VIP User</option>
                    <option value="admin">Admin</option>
                </select>
            </div>
            
            <button type="submit" class="create-btn">
                <i class="fas fa-user-plus"></i> Create Account
            </button>
        </form>

        <div id="status" class="status info">
            <i class="fas fa-info-circle"></i> Select account type and fill in details to create a new account.
        </div>

        <div class="users-list">
            <h3><i class="fas fa-users"></i> Existing Users</h3>
            <div id="usersList">
                Loading users...
            </div>
        </div>

        <div style="margin-top: 2rem;">
            <h3>Quick Actions</h3>
            <button class="create-btn" onclick="createSampleAccounts()" style="margin: 0.5rem 0;">
                Create Sample Accounts
            </button>
            <button class="create-btn" onclick="clearAllUsers()" style="margin: 0.5rem 0; background: #dc3545;">
                Clear All Users (Reset)
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/auth.js"></script>
    
    <script>
        let authManager;
        let selectedRole = 'vip';
        
        document.addEventListener('DOMContentLoaded', function() {
            try {
                authManager = new AuthManager();
                updateStatus('Account Creator ready!', 'success');
                loadUsersList();
            } catch (error) {
                updateStatus('Error initializing: ' + error.message, 'error');
            }
        });

        // Account type selection
        document.querySelectorAll('.account-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.account-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedRole = this.dataset.role;
                document.getElementById('role').value = selectedRole;
            });
        });

        // Form submission
        document.getElementById('createAccountForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                role: document.getElementById('role').value
            };
            
            try {
                // Create user with custom role
                const newUser = {
                    id: Math.max(...authManager.getAllUsers().map(u => u.id), 0) + 1,
                    firstName: formData.firstName.trim(),
                    lastName: formData.lastName.trim(),
                    email: formData.email.toLowerCase().trim(),
                    password: formData.password,
                    role: formData.role,
                    avatar: null,
                    phone: '',
                    address: '',
                    joinDate: new Date().toISOString(),
                    lastLogin: null,
                    status: 'active',
                    preferences: {
                        notifications: true,
                        newsletter: false,
                        darkMode: false
                    },
                    stats: {
                        totalOrders: 0,
                        totalSpent: 0,
                        favoriteItems: 0
                    }
                };
                
                // Check if email already exists
                const existingUser = authManager.getAllUsers().find(u => u.email === newUser.email);
                if (existingUser) {
                    throw new Error('An account with this email already exists');
                }
                
                // Add user directly to the users array
                authManager.users.push(newUser);
                authManager.saveUsers();
                
                updateStatus(`${formData.role.toUpperCase()} account created successfully for ${formData.firstName} ${formData.lastName}!`, 'success');
                
                // Clear form
                document.getElementById('createAccountForm').reset();
                document.getElementById('password').value = 'password123';
                
                // Refresh users list
                loadUsersList();
                
            } catch (error) {
                updateStatus('Error creating account: ' + error.message, 'error');
            }
        });
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'times-circle' : 'info-circle';
            statusDiv.innerHTML = `<i class="fas fa-${icon}"></i> ${message}`;
            statusDiv.className = `status ${type}`;
        }
        
        function loadUsersList() {
            const usersList = document.getElementById('usersList');
            const users = authManager.getAllUsers();
            
            if (users.length === 0) {
                usersList.innerHTML = '<p>No users found.</p>';
                return;
            }
            
            usersList.innerHTML = users.map(user => `
                <div class="user-item">
                    <div>
                        <strong>${user.firstName} ${user.lastName}</strong><br>
                        <small>${user.email}</small>
                    </div>
                    <span class="user-role ${user.role}">${user.role.toUpperCase()}</span>
                </div>
            `).join('');
        }
        
        function createSampleAccounts() {
            const sampleAccounts = [
                { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', role: 'user' },
                { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', role: 'vip' },
                { firstName: 'Bob', lastName: 'Wilson', email: '<EMAIL>', role: 'admin' }
            ];
            
            let created = 0;
            sampleAccounts.forEach(account => {
                const existingUser = authManager.getAllUsers().find(u => u.email === account.email);
                if (!existingUser) {
                    const newUser = {
                        id: Math.max(...authManager.getAllUsers().map(u => u.id), 0) + 1,
                        firstName: account.firstName,
                        lastName: account.lastName,
                        email: account.email,
                        password: 'password123',
                        role: account.role,
                        avatar: null,
                        phone: '',
                        address: '',
                        joinDate: new Date().toISOString(),
                        lastLogin: null,
                        status: 'active',
                        preferences: { notifications: true, newsletter: false, darkMode: false },
                        stats: { totalOrders: 0, totalSpent: 0, favoriteItems: 0 }
                    };
                    authManager.users.push(newUser);
                    created++;
                }
            });
            
            if (created > 0) {
                authManager.saveUsers();
                updateStatus(`Created ${created} sample accounts!`, 'success');
                loadUsersList();
            } else {
                updateStatus('Sample accounts already exist!', 'info');
            }
        }
        
        function clearAllUsers() {
            if (confirm('Are you sure you want to delete all users? This will reset the system to default demo users.')) {
                localStorage.removeItem('theprojectfaith_users');
                authManager = new AuthManager(); // This will reinitialize with demo users
                updateStatus('All users cleared and reset to demo users!', 'info');
                loadUsersList();
            }
        }
    </script>
</body>
</html>
