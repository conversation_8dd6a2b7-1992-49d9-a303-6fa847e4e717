<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Signup Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .debug {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin-top: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Simple Signup Test</h1>
        <p>Test the basic signup functionality</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" value="John" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" value="Doe" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="test123" required>
            </div>
            
            <button type="submit">Create Account</button>
        </form>
        
        <div id="status" class="status info">Ready to test signup...</div>
        <div id="debug" class="debug">Debug info will appear here...</div>
        
        <div style="margin-top: 20px;">
            <button onclick="checkAuthManager()">Check Auth Manager</button>
            <button onclick="listUsers()">List All Users</button>
            <button onclick="clearStorage()">Clear Storage</button>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        let authManager;
        let debugLog = '';
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            debugLog += `[${timestamp}] ${message}\n`;
            document.getElementById('debug').textContent = debugLog;
        }
        
        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, initializing...');
            
            try {
                if (typeof AuthManager !== 'undefined') {
                    authManager = new AuthManager();
                    log('AuthManager initialized successfully');
                    log(`Total users in system: ${authManager.getAllUsers().length}`);
                    updateStatus('Ready to test signup!', 'success');
                } else {
                    log('ERROR: AuthManager class not found');
                    updateStatus('AuthManager not found!', 'error');
                }
            } catch (error) {
                log(`ERROR initializing: ${error.message}`);
                updateStatus('Initialization failed!', 'error');
            }
        });
        
        // Form submission
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            log('Form submitted');
            updateStatus('Creating account...', 'info');
            
            const formData = {
                firstName: document.getElementById('firstName').value,
                lastName: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value
            };
            
            log(`Form data: ${JSON.stringify(formData)}`);
            
            try {
                if (!authManager) {
                    throw new Error('AuthManager not initialized');
                }
                
                log('Calling authManager.signup...');
                
                const user = await authManager.signup({
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    password: formData.password,
                    newsletter: false
                });
                
                log(`SUCCESS: User created with ID ${user.id}`);
                log(`User details: ${JSON.stringify(user, null, 2)}`);
                updateStatus(`Account created successfully! User ID: ${user.id}`, 'success');
                
                // Clear form
                document.getElementById('testForm').reset();
                
                // Update email for next test
                document.getElementById('email').value = `test${Date.now()}@example.com`;
                
            } catch (error) {
                log(`ERROR: ${error.message}`);
                updateStatus(`Signup failed: ${error.message}`, 'error');
            }
        });
        
        function checkAuthManager() {
            log('=== AUTH MANAGER CHECK ===');
            log(`AuthManager exists: ${typeof authManager !== 'undefined'}`);
            log(`AuthManager class exists: ${typeof AuthManager !== 'undefined'}`);
            
            if (authManager) {
                log(`Current user: ${authManager.getCurrentUser() ? 'Logged in' : 'Not logged in'}`);
                log(`Total users: ${authManager.getAllUsers().length}`);
                log(`Auth methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(authManager)).join(', ')}`);
            }
            
            updateStatus('Auth manager check completed', 'info');
        }
        
        function listUsers() {
            if (!authManager) {
                updateStatus('AuthManager not available', 'error');
                return;
            }
            
            log('=== ALL USERS ===');
            const users = authManager.getAllUsers();
            users.forEach((user, index) => {
                log(`${index + 1}. ${user.firstName} ${user.lastName} (${user.email}) - ${user.role}`);
            });
            
            updateStatus(`Listed ${users.length} users`, 'info');
        }
        
        function clearStorage() {
            localStorage.clear();
            log('Local storage cleared');
            updateStatus('Storage cleared - refresh page to reinitialize', 'info');
        }
    </script>
</body>
</html>
