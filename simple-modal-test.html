<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Modal Test</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            background: #4B0082;
            color: white;
        }
        .btn:hover {
            background: #6a1b9a;
        }
        
        /* Simple Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 15px;
        }
        
        .close-modal {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Simple Modal Test</h1>
        <p>Testing a basic Add Product modal to ensure functionality works.</p>
        
        <button class="btn" onclick="showTestModal()">Show Add Product Modal</button>
        <button class="btn" onclick="addTestProduct()">Add Test Product Directly</button>
        
        <div id="status" style="margin-top: 20px; padding: 10px; border-radius: 5px; display: none;"></div>
    </div>

    <!-- Simple Add Product Modal -->
    <div id="testModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Product</h3>
                <button class="close-modal" onclick="closeTestModal()">×</button>
            </div>
            
            <form id="testProductForm" onsubmit="handleTestSubmit(event)">
                <div class="form-group">
                    <label class="form-label">Product Name *</label>
                    <input type="text" id="testProductName" class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Category *</label>
                    <select id="testProductCategory" class="form-select" required>
                        <option value="">Select Category</option>
                        <option value="men">Men's Clothing</option>
                        <option value="women">Women's Clothing</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Price ($) *</label>
                    <input type="number" id="testProductPrice" class="form-input" step="0.01" min="0" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Stock Quantity *</label>
                    <input type="number" id="testProductStock" class="form-input" min="0" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <textarea id="testProductDescription" class="form-textarea" placeholder="Enter product description..."></textarea>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Image URL</label>
                    <input type="url" id="testProductImage" class="form-input" placeholder="https://example.com/image.jpg">
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeTestModal()">Cancel</button>
                    <button type="submit" class="btn">Add Product</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showTestModal() {
            console.log('Showing test modal...');
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('Modal displayed');
            } else {
                console.error('Modal not found');
            }
        }

        function closeTestModal() {
            console.log('Closing test modal...');
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'none';
                document.getElementById('testProductForm').reset();
                console.log('Modal closed');
            }
        }

        function handleTestSubmit(event) {
            event.preventDefault();
            console.log('Form submitted');

            const formData = {
                name: document.getElementById('testProductName').value.trim(),
                category: document.getElementById('testProductCategory').value,
                price: parseFloat(document.getElementById('testProductPrice').value),
                stock: parseInt(document.getElementById('testProductStock').value),
                description: document.getElementById('testProductDescription').value.trim(),
                image: document.getElementById('testProductImage').value.trim()
            };

            console.log('Form data:', formData);

            if (!formData.name || !formData.category || !formData.price || formData.stock < 0) {
                showStatus('Please fill in all required fields', 'error');
                return;
            }

            const product = {
                id: Date.now(),
                name: formData.name,
                brand: 'VAITH',
                description: formData.description || 'No description available',
                category: formData.category,
                status: 'active',
                sku: 'SKU-' + Date.now(),
                price: formData.price,
                originalPrice: null,
                stock: formData.stock,
                images: [formData.image || 'https://via.placeholder.com/400x500?text=No+Image'],
                sizes: ['One Size'],
                colors: ['Default'],
                rating: 0,
                reviews: 0,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString()
            };

            try {
                let products = JSON.parse(localStorage.getItem('vaith_products')) || [];
                products.push(product);
                localStorage.setItem('vaith_products', JSON.stringify(products));

                showStatus(`Product "${formData.name}" added successfully!`, 'success');
                closeTestModal();
                console.log('Product saved:', product);
            } catch (error) {
                console.error('Error saving product:', error);
                showStatus('Error saving product. Please try again.', 'error');
            }
        }

        function addTestProduct() {
            const testProduct = {
                id: Date.now(),
                name: 'Quick Test Product',
                brand: 'The Project Faith',
                description: 'This is a quick test product',
                category: 'women',
                status: 'active',
                sku: 'SKU-QUICK-' + Date.now(),
                price: 19.99,
                originalPrice: null,
                stock: 25,
                images: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=500&fit=crop'],
                sizes: ['S', 'M', 'L'],
                colors: ['Red', 'Blue'],
                rating: 0,
                reviews: 0,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString()
            };

            try {
                let products = JSON.parse(localStorage.getItem('vaith_products')) || [];
                products.push(testProduct);
                localStorage.setItem('vaith_products', JSON.stringify(products));
                showStatus('Quick test product added successfully!', 'success');
                console.log('Quick product added:', testProduct);
            } catch (error) {
                console.error('Error adding quick product:', error);
                showStatus('Error adding quick product.', 'error');
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';
            status.style.background = type === 'success' ? '#d4edda' : '#f8d7da';
            status.style.color = type === 'success' ? '#155724' : '#721c24';
            status.style.border = type === 'success' ? '1px solid #c3e6cb' : '1px solid #f5c6cb';

            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }

        // Close modal when clicking outside
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTestModal();
            }
        });

        console.log('Simple modal test page loaded');
    </script>
</body>
</html>
